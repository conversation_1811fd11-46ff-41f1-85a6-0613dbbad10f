"""
Network Scanner Module
=====================
Automated network reconnaissance and port scanning functionality.
"""

import sys
import socket
import threading
import subprocess
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class NetworkScanner:
    """Automated network reconnaissance and port scanning"""
    
    def __init__(self):
        self.results = []
    
    def ping_check(self, target):
        """Check if target is reachable"""
        try:
            if sys.platform.startswith('win'):
                cmd = ["ping", "-n", "3", target]
            else:
                cmd = ["ping", "-c", "3", target]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            return "0% packet loss" in result.stdout or "0% loss" in result.stdout
        except subprocess.TimeoutExpired:
            return False
        except Exception as e:
            logger.error(f"Ping check failed for {target}: {e}")
            return False
    
    def socket_scan(self, target, ports, timeout=1):
        """Fast socket-based port scan"""
        open_ports = []
        
        def scan_port(port):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(timeout)
                result = sock.connect_ex((target, port))
                if result == 0:
                    open_ports.append(port)
                    logger.info(f"[+] Port {port} open on {target}")
                sock.close()
            except Exception:
                pass
        
        # Threaded scanning for speed
        threads = []
        for port in ports:
            t = threading.Thread(target=scan_port, args=(port,))
            threads.append(t)
            t.start()
        
        for t in threads:
            t.join()
        
        return sorted(open_ports)
    
    def banner_grab(self, target, port, timeout=3):
        """Grab service banner"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            sock.connect((target, port))
            
            # Send HTTP request for web services
            if port in [80, 443, 8080, 8443]:
                sock.send(b"GET / HTTP/1.1\r\nHost: " + target.encode() + b"\r\n\r\n")
            
            banner = sock.recv(1024).decode('utf-8', errors='ignore').strip()
            sock.close()
            return banner[:200]  # Limit banner length
        except Exception:
            return None
    
    def nmap_scan(self, target, ports="22,80,443,8080"):
        """Execute Nmap scan if available"""
        try:
            cmd = ["nmap", "-Pn", "-sS", "-p", ports, target]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            return result.stdout
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("Nmap not available or timed out, using socket scan")
            return None
    
    def comprehensive_scan(self, target):
        """Perform comprehensive target assessment"""
        scan_result = {
            "target": target,
            "timestamp": datetime.now().isoformat(),
            "reachable": False,
            "open_ports": [],
            "banners": {},
            "nmap_output": None
        }
        
        logger.info(f"Starting scan of {target}")
        
        # Ping check
        scan_result["reachable"] = self.ping_check(target)
        if not scan_result["reachable"]:
            logger.warning(f"{target} appears unreachable")
            return scan_result
        
        # Port scan
        common_ports = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 3389, 5900, 8080]
        scan_result["open_ports"] = self.socket_scan(target, common_ports)
        
        # Banner grabbing
        for port in scan_result["open_ports"]:
            banner = self.banner_grab(target, port)
            if banner:
                scan_result["banners"][port] = banner
        
        # Nmap scan
        scan_result["nmap_output"] = self.nmap_scan(target)
        
        self.results.append(scan_result)
        return scan_result
