"""
File Integrity Monitor Module
=============================
File integrity monitoring and alerting functionality.
"""

import os
import json
import hashlib
import logging
from datetime import datetime

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
except ImportError:
    print("Missing dependency: watchdog. Install with: pip install watchdog")
    raise

logger = logging.getLogger(__name__)


class FileIntegrityMonitor:
    """File integrity monitoring and alerting"""
    
    def __init__(self, watch_path="/var/www"):
        self.watch_path = watch_path
        self.baseline_file = "file_baseline.json"
        self.baseline = {}
        self.observer = None
    
    def calculate_file_hash(self, filepath, algorithm="sha256"):
        """Calculate file hash"""
        try:
            hash_obj = getattr(hashlib, algorithm)()
            with open(filepath, "rb") as f:
                chunk = f.read(65536)
                while chunk:
                    hash_obj.update(chunk)
                    chunk = f.read(65536)
            return hash_obj.hexdigest()
        except Exception as e:
            logger.error(f"Error hashing {filepath}: {e}")
            return None
    
    def create_baseline(self, directory):
        """Create file integrity baseline"""
        logger.info(f"Creating baseline for {directory}")
        baseline = {}
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                filepath = os.path.join(root, file)
                try:
                    stat = os.stat(filepath)
                    file_hash = self.calculate_file_hash(filepath)
                    if file_hash:
                        baseline[filepath] = {
                            "hash": file_hash,
                            "size": stat.st_size,
                            "modified": stat.st_mtime,
                            "created": stat.st_ctime
                        }
                except Exception as e:
                    logger.error(f"Error processing {filepath}: {e}")
        
        # Save baseline
        with open(self.baseline_file, "w") as f:
            json.dump(baseline, f, indent=2)
        
        self.baseline = baseline
        logger.info(f"Baseline created with {len(baseline)} files")
    
    def compare_baseline(self):
        """Compare current state to baseline"""
        if not os.path.exists(self.baseline_file):
            logger.error("No baseline found. Create one first.")
            return
        
        with open(self.baseline_file, "r") as f:
            baseline = json.load(f)
        
        changes = {
            "modified": [],
            "deleted": [],
            "added": [],
            "timestamp": datetime.now().isoformat()
        }
        
        # Check for modifications and deletions
        for filepath, baseline_info in baseline.items():
            if not os.path.exists(filepath):
                changes["deleted"].append(filepath)
                logger.warning(f"[DELETED] {filepath}")
            else:
                current_hash = self.calculate_file_hash(filepath)
                if current_hash and current_hash != baseline_info["hash"]:
                    changes["modified"].append(filepath)
                    logger.warning(f"[MODIFIED] {filepath}")
        
        # Check for new files
        for root, dirs, files in os.walk(self.watch_path):
            for file in files:
                filepath = os.path.join(root, file)
                if filepath not in baseline:
                    changes["added"].append(filepath)
                    logger.warning(f"[ADDED] {filepath}")
        
        return changes
    
    def start_realtime_monitoring(self):
        """Start real-time file monitoring"""
        class FSEventHandler(FileSystemEventHandler):
            def __init__(self, monitor):
                self.monitor = monitor
            
            def on_modified(self, event):
                if not event.is_directory:
                    logger.warning(f"[RT-MODIFIED] {event.src_path}")
            
            def on_created(self, event):
                if not event.is_directory:
                    logger.warning(f"[RT-CREATED] {event.src_path}")
                    # Alert on suspicious file types
                    if any(event.src_path.endswith(ext) for ext in ['.php', '.exe', '.sh', '.bat']):
                        logger.critical(f"[ALERT] Suspicious file created: {event.src_path}")
        
        self.observer = Observer()
        event_handler = FSEventHandler(self)
        self.observer.schedule(event_handler, self.watch_path, recursive=True)
        self.observer.start()
        logger.info(f"Real-time monitoring started for {self.watch_path}")
    
    def stop_realtime_monitoring(self):
        """Stop real-time monitoring"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            logger.info("Real-time monitoring stopped")
