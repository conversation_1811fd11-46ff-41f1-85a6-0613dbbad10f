"""
Report Generator Module
======================
Generate comprehensive security reports in various formats.
"""

import json
import logging
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)


class ReportGenerator:
    """Generate comprehensive security reports"""
    
    def __init__(self):
        self.reports_dir = Path("reports")
        self.reports_dir.mkdir(exist_ok=True)
    
    def generate_scan_report(self, scan_results, output_format="json"):
        """Generate network scan report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        report = {
            "report_type": "network_scan",
            "generated_at": datetime.now().isoformat(),
            "summary": {
                "targets_scanned": len(scan_results),
                "reachable_hosts": sum(1 for r in scan_results if r["reachable"]),
                "total_open_ports": sum(len(r["open_ports"]) for r in scan_results)
            },
            "results": scan_results
        }
        
        if output_format == "json":
            filename = self.reports_dir / f"scan_report_{timestamp}.json"
            with open(filename, "w") as f:
                json.dump(report, f, indent=2)
        elif output_format == "html":
            filename = self.reports_dir / f"scan_report_{timestamp}.html"
            self.generate_html_report(report, filename)
        
        logger.info(f"Scan report generated: {filename}")
        return filename
    
    def generate_html_report(self, data, filename):
        """Generate HTML report"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>SOC Automation Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; }}
                .summary {{ margin: 20px 0; padding: 15px; border: 1px solid #ccc; }}
                .critical {{ color: red; font-weight: bold; }}
                .warning {{ color: orange; }}
                .info {{ color: blue; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>SOC Automation Report</h1>
                <p>Generated: {data['generated_at']}</p>
                <p>Report Type: {data['report_type']}</p>
            </div>
            <div class="summary">
                <h2>Summary</h2>
                <p>Targets Scanned: {data['summary']['targets_scanned']}</p>
                <p>Reachable Hosts: {data['summary']['reachable_hosts']}</p>
                <p>Total Open Ports: {data['summary']['total_open_ports']}</p>
            </div>
            <div class="details">
                <h2>Detailed Results</h2>
                <table>
                    <tr>
                        <th>Target</th>
                        <th>Reachable</th>
                        <th>Open Ports</th>
                        <th>Services</th>
                    </tr>
        """
        
        for result in data['results']:
            ports_str = ', '.join(map(str, result['open_ports']))
            services = ', '.join(f"{port}:{banner[:50]}..." 
                               for port, banner in result['banners'].items())
            
            html_content += f"""
                    <tr>
                        <td>{result['target']}</td>
                        <td>{'YES' if result['reachable'] else 'NO'}</td>
                        <td>{ports_str}</td>
                        <td>{services}</td>
                    </tr>
            """
        
        html_content += """
                </table>
            </div>
        </body>
        </html>
        """
        
        with open(filename, "w", encoding='utf-8') as f:
            f.write(html_content)
