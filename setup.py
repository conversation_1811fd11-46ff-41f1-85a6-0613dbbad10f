#!/usr/bin/env python3
"""
Setup script for SOC Automation Suite
=====================================
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# Read requirements
requirements = []
with open('requirements.txt', 'r') as f:
    for line in f:
        line = line.strip()
        if line and not line.startswith('#'):
            requirements.append(line)

setup(
    name="soc-automation-suite",
    version="1.0.0",
    author="Security Team",
    author_email="<EMAIL>",
    description="A comprehensive security automation toolkit",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/company/soc-automation-suite",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Information Technology",
        "Topic :: Security",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "soc-automation=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "soc_automation": ["*.py"],
    },
    keywords="security automation soc network scanning threat intelligence file monitoring",
    project_urls={
        "Bug Reports": "https://github.com/company/soc-automation-suite/issues",
        "Source": "https://github.com/company/soc-automation-suite",
        "Documentation": "https://github.com/company/soc-automation-suite/wiki",
    },
)
