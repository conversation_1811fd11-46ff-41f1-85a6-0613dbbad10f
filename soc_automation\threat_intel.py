"""
Threat Intelligence Module
==========================
Threat intelligence API integration for IP and domain reputation checks.
"""

import os
import subprocess
import logging
from datetime import datetime

try:
    import requests
except ImportError:
    print("Missing dependency: requests. Install with: pip install requests")
    raise

logger = logging.getLogger(__name__)


class ThreatIntelligence:
    """Threat intelligence API integration"""
    
    def __init__(self):
        # Note: Add your API keys here
        self.virustotal_key = os.getenv('VT_API_KEY', '')
        self.abuseipdb_key = os.getenv('ABUSEIPDB_KEY', '')
    
    def check_ip_reputation(self, ip):
        """Check IP reputation using multiple sources"""
        reputation = {
            "ip": ip,
            "timestamp": datetime.now().isoformat(),
            "sources": {}
        }
        
        # IPInfo.io (free tier)
        try:
            response = requests.get(f"https://ipinfo.io/{ip}/json", timeout=10)
            if response.status_code == 200:
                data = response.json()
                reputation["sources"]["ipinfo"] = {
                    "city": data.get("city"),
                    "country": data.get("country"),
                    "org": data.get("org"),
                    "asn": data.get("asn")
                }
        except Exception as e:
            logger.error(f"IPInfo lookup failed for {ip}: {e}")
        
        # AbuseIPDB (if API key available)
        if self.abuseipdb_key:
            try:
                headers = {
                    'Key': self.abuseipdb_key,
                    'Accept': 'application/json'
                }
                params = {
                    'ipAddress': ip,
                    'maxAgeInDays': 90,
                    'verbose': ''
                }
                response = requests.get(
                    'https://api.abuseipdb.com/api/v2/check',
                    headers=headers,
                    params=params,
                    timeout=10
                )
                if response.status_code == 200:
                    data = response.json()
                    reputation["sources"]["abuseipdb"] = {
                        "abuse_confidence": data.get("data", {}).get("abuseConfidencePercentage", 0),
                        "is_public": data.get("data", {}).get("isPublic", False),
                        "total_reports": data.get("data", {}).get("totalReports", 0)
                    }
            except Exception as e:
                logger.error(f"AbuseIPDB lookup failed for {ip}: {e}")
        
        return reputation
    
    def check_domain_reputation(self, domain):
        """Check domain reputation"""
        try:
            result = subprocess.run(["whois", domain], capture_output=True, text=True, timeout=15)
            if result.returncode == 0:
                return {"domain": domain, "whois": result.stdout[:1000]}  # Limit output
        except Exception as e:
            logger.error(f"WHOIS lookup failed for {domain}: {e}")
        return None
