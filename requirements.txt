# SOC Automation Suite Dependencies
# Install with: pip install -r requirements.txt

# Core dependencies
setuptools>=59.0.0
requests>=2.28.0
schedule>=1.2.0
watchdog>=2.1.0

# Optional dependencies for enhanced functionality
# python-nmap>=0.7.1  # For advanced Nmap integration
# psutil>=5.9.0       # For system monitoring
# colorama>=0.4.4     # For colored terminal output

# Development dependencies (uncomment for development)
# pytest>=7.0.0
# pytest-cov>=4.0.0
# black>=22.0.0
# flake8>=5.0.0
