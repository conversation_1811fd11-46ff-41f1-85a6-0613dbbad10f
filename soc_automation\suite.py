"""
SOC Automation Suite Main Orchestrator
======================================
Main orchestrator for SOC automation tasks including scheduling and coordination.
"""

import json
import time
import logging
from datetime import datetime

try:
    import schedule
except ImportError:
    print("Missing dependency: schedule. Install with: pip install schedule")
    raise

from .scanner import NetworkScanner
from .threat_intel import ThreatIntelligence
from .file_monitor import FileIntegrityMonitor
from .reporter import ReportGenerator

logger = logging.getLogger(__name__)


class SOCAutomationSuite:
    """Main orchestrator for SOC automation tasks"""
    
    def __init__(self):
        self.scanner = NetworkScanner()
        self.threat_intel = ThreatIntelligence()
        self.file_monitor = FileIntegrityMonitor()
        self.reporter = ReportGenerator()
        self.running = False
    
    def scheduled_network_scan(self):
        """Scheduled network scanning task"""
        targets = ["*******", "*******", "scanme.nmap.org"]
        logger.info("Starting scheduled network scan")
        
        scan_results = []
        for target in targets:
            result = self.scanner.comprehensive_scan(target)
            scan_results.append(result)
            
            # Check threat intelligence for IPs
            if result["reachable"]:
                threat_data = self.threat_intel.check_ip_reputation(target)
                logger.info(f"Threat intel for {target}: {threat_data}")
        
        # Generate report
        self.reporter.generate_scan_report(scan_results, "json")
        self.reporter.generate_scan_report(scan_results, "html")
    
    def scheduled_integrity_check(self):
        """Scheduled file integrity check"""
        logger.info("Starting scheduled integrity check")
        changes = self.file_monitor.compare_baseline()
        if changes:
            total_changes = (len(changes["modified"]) + 
                           len(changes["deleted"]) + 
                           len(changes["added"]))
            if total_changes > 0:
                logger.critical(f"File integrity check found {total_changes} changes")
                
                # Save changes report
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                changes_file = f"reports/integrity_changes_{timestamp}.json"
                with open(changes_file, "w") as f:
                    json.dump(changes, f, indent=2)
    
    def start_monitoring(self):
        """Start continuous monitoring"""
        logger.info("Starting SOC Automation Suite")
        self.running = True
        
        # Schedule tasks
        schedule.every(4).hours.do(self.scheduled_network_scan)
        schedule.every(2).hours.do(self.scheduled_integrity_check)
        
        # Start real-time file monitoring
        self.file_monitor.start_realtime_monitoring()
        
        # Main loop
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            logger.info("Shutting down...")
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """Stop all monitoring"""
        self.running = False
        self.file_monitor.stop_realtime_monitoring()
        logger.info("SOC Automation Suite stopped")
